"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Check, X, Loader2, Zap } from "lucide-react";
import { useTranslations } from "next-intl";
import { subscriptionService } from "@/services/api/subscriptionService";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import { trackEvent } from "@/lib/analytics";
import { safeOpenUrl } from "@/lib/browserUtils";

const FreeUserUpgradeDialog = ({ isOpen, onOpenChange, limitType }) => {
  const t = useTranslations("dashboard.freeUserUpgradeDialog");
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState("yearly"); // 默认选择年付
  const { openDialog } = useUpgradeDialogStore();

  const getDialogContent = () => {
    switch (limitType) {
      case "minutes":
        return {
          description: t("minutes.description"),
        };
      case "daily_count":
        return {
          description: t("daily_count.description"),
        };
      default:
        return {
          description: t("default.description"),
        };
    }
  };

  const content = getDialogContent();

  // 统一的关闭处理函数，包含事件追踪
  const handleClose = (closeMethod = "unknown") => {
    // 跟踪关闭事件
    trackEvent("free_user_upgrade_dialog_close", {
      limitType: limitType,
      source: "free_user_upgrade_dialog",
      closeMethod: closeMethod, // "close_button", "escape_key", "upgrade_success", "see_all_plans"
    });

    onOpenChange(false);
  };

  // 添加键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Escape" && isOpen) {
        handleClose("escape_key");
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onOpenChange]);

  const handleUpgrade = async () => {
    setIsUpgrading(true);
    try {
      // 根据选择的计划类型确定 plan ID
      const planId =
        selectedPlan === "yearly" ? "basic_yearly" : "basic_monthly";

      const response = await subscriptionService.createCheckoutSession(
        planId,
        null, // 不使用促销码
        "subscription",
        "free_user_upgrade_dialog"
      );

      const url = response.data.url;
      if (url) {
        safeOpenUrl(url);
        handleClose("upgrade_success");
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);

      // 跟踪升级失败事件
      trackEvent("free_user_upgrade_failed", {
        limitType: limitType,
        source: "free_user_upgrade_dialog",
        selectedPlan: selectedPlan,
        error: error.message || "Unknown error",
      });

      // 可以在这里添加用户友好的错误提示
      // 例如显示一个toast或者错误消息
    } finally {
      setIsUpgrading(false);
    }
  };

  const handleSeeAllPlans = () => {
    openDialog({
      source: "free_user_upgrade_dialog_see_all_plans",
      defaultPlanType: "yearly",
      title: "",
      description: "",
    });

    handleClose("see_all_plans");
  };

  // 移除点击背景关闭功能 - 用户需要明确选择操作

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/30 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-lg bg-white border-0 shadow-2xl max-h-[90vh] overflow-y-auto">
        <CardContent className="p-6 text-center">
          {/* 关闭按钮 */}
          <div className="flex justify-end mb-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleClose("close_button")}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* 顶部：警示图标、标题、描述 */}
          <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-indigo-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {t("title")}
          </h2>
          <p className="text-gray-600 mb-6">{content.description}</p>

          {/* 中部：功能列表 */}
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-4">
              <div className="bg-indigo-600 text-white px-3 py-1 rounded-md text-sm font-bold flex items-center gap-1">
                <Zap className="w-4 h-4" />
                FLASH
              </div>
              <h3 className="text-lg font-bold text-gray-900">
                {t("planTitle")}
              </h3>
            </div>
            <div className="space-y-3 text-left">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-4 h-4 text-white" />
                </div>
                <span className="text-gray-700 font-medium">
                  {t("features.minutes")}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-4 h-4 text-white" />
                </div>
                <span className="text-gray-700 font-medium">
                  {t("features.premiumModel")}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-4 h-4 text-white" />
                </div>
                <span className="text-gray-700 font-medium">
                  {t("features.speakerIdentification")}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="w-4 h-4 text-white" />
                </div>
                <span className="text-gray-700 font-medium">
                  {t("features.prioritySupport")}
                </span>
              </div>
            </div>
          </div>

          {/* 价格选择区域 */}
          <div className="mb-6">
            <div className="space-y-3">
              {/* 年付选项 */}
              <div
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                  selectedPlan === "yearly"
                    ? "border-indigo-600 bg-indigo-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setSelectedPlan("yearly")}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                        selectedPlan === "yearly"
                          ? "border-indigo-600 bg-indigo-600"
                          : "border-gray-300"
                      }`}
                    >
                      {selectedPlan === "yearly" && (
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      )}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">
                        {t("pricing.yearly")}
                      </div>
                      <div className="text-gray-600">
                        {t("pricing.yearlyPrice")}
                      </div>
                    </div>
                  </div>
                  <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {t("pricing.save40")}
                  </div>
                </div>
              </div>

              {/* 月付选项 */}
              <div
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                  selectedPlan === "monthly"
                    ? "border-indigo-600 bg-indigo-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setSelectedPlan("monthly")}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                      selectedPlan === "monthly"
                        ? "border-indigo-600 bg-indigo-600"
                        : "border-gray-300"
                    }`}
                  >
                    {selectedPlan === "monthly" && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {t("pricing.monthly")}
                    </div>
                    <div className="text-gray-600">
                      {t("pricing.monthlyPrice")}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 底部：升级按钮和查看所有计划按钮 */}
          <div>
            <Button
              className="w-full py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-bold shadow-lg mb-3 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleUpgrade}
              disabled={isUpgrading}
            >
              {isUpgrading ? (
                <div className="flex items-center justify-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  {t("upgrading")}
                </div>
              ) : (
                <div className="flex items-center justify-center gap-2">
                  🚀 {t("upgradeButton")}
                  {selectedPlan === "yearly" ? " ($72/year)" : " ($10/month)"}
                </div>
              )}
            </Button>
            <button
              className="text-sm text-gray-500 hover:text-gray-700 underline"
              onClick={handleSeeAllPlans}
            >
              {t("seeAllPlans")}
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FreeUserUpgradeDialog;
